<script setup lang="ts">

const getApiBaseUrl = (): string => {
  // Try runtime config first (from window.APP_CONFIG)
  if (typeof window !== 'undefined' && window.APP_CONFIG?.API_BASE_URL) {
    return window.APP_CONFIG.API_BASE_URL;
  }

  // Fallback to Vite environment variable (build-time)
  return import.meta.env.VITE_API_BASE_URL;
};

const loginWithLine = async () => {
  try {
    const apiBaseUrl = getApiBaseUrl()
    const response = await fetch(`${apiBaseUrl}/api/auth/line`)
    const data = await response.json()
    if (data.url) {
      window.location.href = data.url
    }
  } catch (error) {
    console.error('Error initiating Line login:', error)
  }
}
</script>

<template>
  <div class="line-login">
    <button @click="loginWithLine" class="line-login-button">
      <span class="button-icon">L</span>
      <span class="button-text">Login with LINE</span>
    </button>
  </div>
</template>

<style scoped>
.line-login {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 1rem 0;
  width: 100%;
}

.line-login-button {
  background: #06C755;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  padding: 12px 24px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  min-width: 200px;
  width: 100%;
  max-width: 300px;
}

.line-login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(6, 199, 85, 0.2);
  background: #05b34a;
}

.line-login-button:active {
  transform: translateY(0);
}

.button-icon {
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
}

.button-text {
  color: white;
  font-size: 1rem;
  font-weight: 500;
}
</style> 