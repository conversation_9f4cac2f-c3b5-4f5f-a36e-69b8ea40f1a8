docker build -t rubyhcm/frontend-line:latest .

docker push rubyhcm/frontend-line:latest

kubectl apply -f k8s/
--------
kubectl get pods -n line

kubectl get services -n line

kubectl describe service frontend-service -n line

kubectl port-forward service/frontend-service 30001:80 -n line

minikube service frontend-service -n=line
----------

chmod +x build-docker.sh
./build-docker.sh

-----
kubectl rollout restart deployment frontend-deployment -n line => to restart the deployment
kubectl delete pod frontend-pod -n line && kubectl apply -f k8s/pod.yaml => to restart the pod

-----
kubectl exec frontend-pod -n line -- cat /usr/share/nginx/html/config.js => to view a file


